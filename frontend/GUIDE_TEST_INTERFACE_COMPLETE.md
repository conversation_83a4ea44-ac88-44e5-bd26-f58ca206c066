# 🧪 GUIDE DE TEST COMPLET - INTERFACE GOLD SENTINEL

## 🚀 PRÉPARATION DES TESTS

### 1. Démarrage du système
```bash
# Terminal 1 - Backend Django
cd /path/to/gold-sentinel_v3
python manage.py runserver 8000

# Terminal 2 - Frontend React
cd frontend
npm run dev
```

### 2. Accès à l'interface
- **URL**: http://localhost:5173
- **Backend API**: http://localhost:8000/api/v1/

---

## 🔐 TESTS D'AUTHENTIFICATION

### Page de Connexion (`/login`)

**Ce qui doit s'afficher :**
- Formulaire avec champs email/mot de passe
- Logo Gold Sentinel
- Bouton "Se connecter"
- Design moderne avec dégradés bleus

**Tests à effectuer :**

1. **Connexion valide**
   - Email: `<EMAIL>`
   - Mot de passe: `admin123`
   - **Résultat attendu**: Redirection vers `/dashboard`
   - **Toast**: "Bienvenue, [Nom Utilisateur]!"

2. **Connexion invalide**
   - Email: `<EMAIL>`
   - Mot de passe: `wrongpass`
   - **Résultat attendu**: Message d'erreur "Identifiants incorrects"

3. **Champs vides**
   - **Résultat attendu**: Validation côté client, bouton désactivé

---

## 📊 TESTS DU DASHBOARD PRINCIPAL

### Page Dashboard (`/dashboard`)

**Ce qui doit s'afficher :**

1. **Header avec navigation**
   - Logo Gold Sentinel
   - Menu de navigation (Dashboard, Images, Détections, etc.)
   - Avatar utilisateur avec dropdown

2. **Cartes de statistiques (4 cartes principales)**
   - Total Détections
   - Alertes Actives  
   - Investigations En Cours
   - Impact Financier (en FCFA)

3. **Graphiques**
   - Tendances des détections (ligne temporelle)
   - Répartition par type de détection (camembert)
   - Alertes par niveau de gravité

4. **Tableau des détections récentes**
   - Colonnes: Date, Type, Confiance, Statut, Actions

**Tests à effectuer :**

1. **Chargement des données**
   - **Action**: Rafraîchir la page
   - **Résultat attendu**: Données chargées depuis `/api/v1/stats/dashboard/`
   - **Indicateur**: Pas de skeleton loaders après 2-3 secondes

2. **Permissions par rôle**
   - **Administrateur**: Voit tout
   - **Responsable Régional**: Voit statistiques + bouton "Lancer Analyse"
   - **Agent Analyste**: Voit statistiques, pas de bouton analyse
   - **Agent Terrain**: Voit alertes et investigations assignées

3. **Interactions**
   - Clic sur carte → Navigation vers page détaillée
   - Hover sur graphiques → Tooltips informatifs

---

## 🖼️ TESTS DES IMAGES SATELLITES

### Page Images (`/images`)

**Ce qui doit s'afficher :**
- Liste des images satellites avec miniatures
- Filtres: Source satellite, Date, Statut de traitement
- Pagination
- Détails: Date capture, résolution, couverture nuageuse

**Tests à effectuer :**

1. **Chargement de la liste**
   - **Endpoint**: `GET /api/v1/images/`
   - **Résultat attendu**: Liste paginée d'images

2. **Filtres fonctionnels**
   - Filtre par source (Sentinel-2, Landsat-8)
   - Filtre par date
   - **Résultat attendu**: Liste mise à jour dynamiquement

3. **Détail d'une image**
   - Clic sur image → Modal ou page détail
   - **Contenu**: Métadonnées complètes, preview haute résolution

---

## 🔍 TESTS DES DÉTECTIONS

### Page Détections (`/detections`)

**Ce qui doit s'afficher :**
- Tableau des détections avec colonnes:
  - ID, Date, Type, Coordonnées, Confiance, Statut, Actions
- Filtres avancés
- Carte interactive avec marqueurs
- Boutons d'action (Valider, Rejeter)

**Tests à effectuer :**

1. **Liste des détections**
   - **Endpoint**: `GET /api/v1/detections/`
   - **Permissions**: Responsable Régional uniquement
   - **Résultat attendu**: Liste complète avec pagination

2. **Validation d'une détection**
   - **Action**: Clic sur "Valider" 
   - **Endpoint**: `POST /api/v1/detections/{id}/validate/`
   - **Body**: `{"validation_status": "CONFIRMED"}`
   - **Résultat attendu**: Statut mis à jour, toast de confirmation

3. **Filtres**
   - Par type de détection
   - Par niveau de confiance
   - Par statut de validation
   - **Résultat attendu**: URL mise à jour avec paramètres

4. **Carte interactive**
   - Marqueurs colorés selon type de détection
   - Popup avec détails au clic
   - Zoom sur région BONDOUKOU

---

## 🚨 TESTS DES ALERTES

### Page Alertes (`/alerts`)

**Ce qui doit s'afficher :**
- Liste des alertes avec niveaux de priorité
- Indicateurs visuels (couleurs, icônes)
- Actions: Marquer comme lue, Résoudre
- Filtres par niveau et statut

**Tests à effectuer :**

1. **Liste des alertes**
   - **Endpoint**: `GET /api/v1/alerts/`
   - **Permissions**: Responsable + Agent Terrain
   - **Résultat attendu**: Alertes triées par priorité

2. **Mise à jour du statut**
   - **Action**: Clic sur "Marquer comme lue"
   - **Endpoint**: `PATCH /api/v1/alerts/{id}/status/`
   - **Body**: `{"alert_status": "ACKNOWLEDGED"}`
   - **Résultat attendu**: Statut visuel mis à jour

3. **Notifications temps réel**
   - **Résultat attendu**: Badge de notification sur l'icône cloche
   - **Comportement**: Mise à jour automatique toutes les 30 secondes

---

## 🔎 TESTS DES INVESTIGATIONS

### Page Investigations (`/investigations`)

**Ce qui doit s'afficher :**
- Liste des investigations avec statuts
- Assignation aux agents terrain
- Suivi des progressions
- Formulaires de rapport

**Tests à effectuer :**

1. **Liste des investigations**
   - **Endpoint**: `GET /api/v1/investigations/`
   - **Résultat attendu**: Investigations avec détails d'assignation

2. **Assignation d'agent**
   - **Action**: Sélection agent + clic "Assigner"
   - **Endpoint**: `PATCH /api/v1/investigations/{id}/assign/`
   - **Body**: `{"assigned_to": agent_id, "priority": "HIGH"}`
   - **Résultat attendu**: Investigation assignée, notification envoyée

3. **Mes investigations (Agent Terrain)**
   - **Endpoint**: `GET /api/v1/investigations/assigned-to-me/`
   - **Résultat attendu**: Seules les investigations assignées à l'utilisateur

4. **Mise à jour du statut**
   - **Statuts**: PENDING → ASSIGNED → IN_PROGRESS → COMPLETED
   - **Résultat attendu**: Progression visuelle mise à jour

---

## 📈 TESTS D'ANALYSE

### Page Analyse (`/analysis`)

**Ce qui doit s'afficher :**
- Formulaire de lancement d'analyse
- Paramètre: Nombre de mois (1-12)
- Bouton "Lancer l'analyse"
- Historique des analyses précédentes

**Tests à effectuer :**

1. **Lancement d'analyse**
   - **Permissions**: Responsable Régional + Administrateur uniquement
   - **Action**: Sélection 3 mois + clic "Lancer"
   - **Endpoint**: `POST /api/v1/analysis/run/`
   - **Body**: `{"months_back": 3}`
   - **Résultat attendu**: 
     - Message de confirmation
     - Indicateur de progression
     - Redirection vers résultats

2. **Validation des paramètres**
   - **Test**: Saisie de 15 mois
   - **Résultat attendu**: Erreur "months_back doit être entre 1 et 12"

3. **Résultats d'analyse**
   - **Contenu attendu**:
     - Nombre d'images traitées
     - Détections créées
     - Alertes générées
     - Impact financier estimé
     - Investigations créées

---

## 📊 TESTS DES STATISTIQUES

### Page Statistiques (`/stats`)

**Ce qui doit s'afficher :**
- Graphiques avancés
- Métriques détaillées
- Rapports exportables
- Comparaisons temporelles

**Tests à effectuer :**

1. **Chargement des statistiques**
   - **Endpoints multiples**:
     - `/api/v1/stats/summary/`
     - `/api/v1/stats/detection-trends/`
     - `/api/v1/stats/financial-impact/`
   - **Résultat attendu**: Graphiques interactifs chargés

2. **Filtres temporels**
   - **Options**: 7 jours, 30 jours, 3 mois, 1 an
   - **Résultat attendu**: Données mises à jour selon période

3. **Export de rapports**
   - **Formats**: PDF, Excel
   - **Résultat attendu**: Téléchargement automatique

---

## 🔧 TESTS DE NAVIGATION ET UX

### Navigation générale

**Tests à effectuer :**

1. **Menu de navigation**
   - **Action**: Clic sur chaque élément du menu
   - **Résultat attendu**: Navigation fluide, URL mise à jour

2. **Permissions d'accès**
   - **Test**: Accès direct via URL à page non autorisée
   - **Résultat attendu**: Redirection vers dashboard ou erreur 403

3. **Responsive design**
   - **Test**: Redimensionnement fenêtre
   - **Résultat attendu**: Interface adaptée mobile/tablet

4. **Déconnexion**
   - **Action**: Clic sur "Déconnexion" dans dropdown utilisateur
   - **Résultat attendu**: Redirection vers `/login`, tokens supprimés

---

## ⚠️ TESTS D'ERREURS

### Gestion des erreurs

**Tests à effectuer :**

1. **Backend indisponible**
   - **Action**: Arrêter le serveur Django
   - **Résultat attendu**: Messages d'erreur gracieux, pas de crash

2. **Token expiré**
   - **Simulation**: Supprimer token localStorage
   - **Résultat attendu**: Redirection automatique vers login

3. **Erreurs réseau**
   - **Simulation**: Déconnexion internet
   - **Résultat attendu**: Messages d'erreur informatifs

---

## ✅ CHECKLIST DE VALIDATION FINALE

- [ ] Authentification fonctionne
- [ ] Dashboard charge les statistiques
- [ ] Navigation entre pages fluide
- [ ] Permissions respectées par rôle
- [ ] API calls utilisent `/api/v1/`
- [ ] Gestion d'erreurs gracieuse
- [ ] Interface responsive
- [ ] Notifications toast fonctionnelles
- [ ] Déconnexion propre
- [ ] Pas d'erreurs console JavaScript

---

## 🐛 PROBLÈMES COURANTS

### Si l'interface ne charge pas :
1. Vérifier que le backend Django tourne sur port 8000
2. Vérifier les variables d'environnement `.env.development`
3. Vérifier la console navigateur pour erreurs CORS

### Si les API calls échouent :
1. Vérifier l'URL de base dans `axios.config.ts`
2. Vérifier les tokens d'authentification
3. Vérifier les permissions utilisateur

### Si les données ne s'affichent pas :
1. Vérifier que la base de données contient des données
2. Lancer une analyse via l'interface pour générer des données
3. Vérifier les serializers backend

---

**🎯 OBJECTIF**: Toutes les fonctionnalités doivent être opérationnelles avec le backend réel, sans données mockées.
