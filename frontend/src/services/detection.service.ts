import axiosInstance from './axios.config';
import type { Detection, DetectionResponse, DetectionStats, ValidationResponse } from '../types/detection.types';

class DetectionService {
  async getDetections(params?: any): Promise<DetectionResponse> {
    try {
      const response = await axiosInstance.get('/detections/', { params });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération détections:', error);
      throw new Error('Impossible de récupérer les détections');
    }
  }

  async getDetection(id: number): Promise<Detection> {
    try {
      const response = await axiosInstance.get(`/detections/${id}/`);
      return response.data;
    } catch (error) {
      console.error('Erreur récupération détection:', error);
      throw new Error('Impossible de récupérer la détection');
    }
  }

  async getHighConfidenceDetections(): Promise<DetectionResponse> {
    try {
      const response = await axiosInstance.get('/detections/high-confidence/');
      return response.data;
    } catch (error) {
      console.error('Erreur récupération détections haute confiance:', error);
      throw new Error('Impossible de récupérer les détections haute confiance');
    }
  }

  async validateDetection(id: number, validationStatus: string, comment?: string): Promise<ValidationResponse> {
    try {
      const response = await axiosInstance.patch(`/detections/${id}/validate/`, {
        validation_status: validationStatus,
        comment: comment
      });
      return response.data;
    } catch (error) {
      console.error('Erreur validation détection:', error);
      throw new Error('Impossible de valider la détection');
    }
  }

  async deleteDetection(id: number): Promise<void> {
    try {
      await axiosInstance.delete(`/detections/${id}/`);
    } catch (error) {
      console.error('Erreur suppression détection:', error);
      throw new Error('Impossible de supprimer la détection');
    }
  }

  // Nouvelles méthodes pour les filtres avancés
  async getDetectionsByRegion(regionId: number): Promise<DetectionResponse> {
    try {
      const response = await axiosInstance.get('/detections/', {
        params: { region: regionId },
      });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération détections par région:', error);
      throw new Error('Impossible de récupérer les détections par région');
    }
  }

  async getDetectionsByType(detectionType: string): Promise<DetectionResponse> {
    try {
      const response = await axiosInstance.get('/detections/', {
        params: { detection_type: detectionType },
      });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération détections par type:', error);
      throw new Error('Impossible de récupérer les détections par type');
    }
  }

  async getDetectionsByStatus(validationStatus: string): Promise<DetectionResponse> {
    try {
      const response = await axiosInstance.get('/detections/', {
        params: { validation_status: validationStatus },
      });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération détections par statut:', error);
      throw new Error('Impossible de récupérer les détections par statut');
    }
  }

  // Méthodes pour les statistiques
  async getDetectionStats(): Promise<DetectionStats> {
    try {
      const response = await axiosInstance.get('/detections/stats/');
      return response.data;
    } catch (error) {
      console.error('Erreur récupération stats détections:', error);
      throw new Error('Impossible de récupérer les statistiques de détection');
    }
  }
}

export default new DetectionService();