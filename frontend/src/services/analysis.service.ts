import axiosInstance from './axios.config';

// Types pour l'analyse spectrale selon spécifications DoD
export interface AnalysisZone {
  id?: number;
  name: string;
  coordinates: [number, number][];
  area_hectares: number;
  center_lat: number;
  center_lng: number;
  created_at?: string;
}

export interface AnalysisParameters {
  zone_id?: number;
  custom_zone?: AnalysisZone;
  start_date: string;
  end_date: string;
  cloud_coverage_max: number;
  detection_sensitivity: 'LOW' | 'MEDIUM' | 'HIGH';
  analysis_type: 'MINING_DETECTION' | 'CHANGE_DETECTION' | 'VEGETATION_ANALYSIS';
  satellite_sources: string[];
  spectral_indices: string[];
}

export interface AnalysisJob {
  id: string;
  name: string;
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  progress: number;
  parameters: AnalysisParameters;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  results_url?: string;
  created_by: number;
  created_by_name: string;
}

export interface AnalysisResult {
  id: string;
  job_id: string;
  total_images_processed: number;
  detections_found: number;
  alerts_generated: number;
  investigations_created: number;
  analysis_date: string;
  processing_time_seconds: number;
  coverage_area_hectares: number;
  detection_summary: {
    mining_sites: number;
    water_pollution: number;
    deforestation: number;
    soil_disturbance: number;
  };
  confidence_distribution: {
    high: number;
    medium: number;
    low: number;
  };
  geojson_results?: any;
  thumbnail_url?: string;
  full_report_url?: string;
}

export interface SatelliteSource {
  id: string;
  name: string;
  description: string;
  resolution_meters: number;
  revisit_days: number;
  available: boolean;
  bands: string[];
}

export interface SpectralIndex {
  id: string;
  name: string;
  description: string;
  formula: string;
  use_case: string;
  bands_required: string[];
}

// Nouveaux types pour Sprint 6 : Analyses & Risques Financiers
export interface FinancialRiskAssessment {
  id: string;
  analysis_job_id: string;
  total_estimated_damage: number;
  currency: string;
  risk_level: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  confidence_score: number;
  assessment_date: string;
  breakdown: {
    environmental_damage: number;
    lost_productivity: number;
    remediation_costs: number;
    legal_penalties: number;
    reputation_damage: number;
  };
  recommendations: string[];
  created_by: number;
  created_by_name: string;
}

export interface AnalysisTemplate {
  id: string;
  name: string;
  description: string;
  parameters: AnalysisParameters;
  is_public: boolean;
  created_by: number;
  created_by_name: string;
  usage_count: number;
  created_at: string;
}

export interface AnalysisSchedule {
  id: string;
  name: string;
  template_id: string;
  cron_expression: string;
  is_active: boolean;
  next_run: string;
  last_run?: string;
  created_by: number;
  created_by_name: string;
}

export interface AnalysisStats {
  total_analyses: number;
  completed_analyses: number;
  failed_analyses: number;
  running_analyses: number;
  total_processing_time_hours: number;
  total_area_analyzed_hectares: number;
  total_detections: number;
  total_alerts_generated: number;
  analyses_by_type: {
    MINING_DETECTION: number;
    CHANGE_DETECTION: number;
    VEGETATION_ANALYSIS: number;
  };
  monthly_trend: Array<{
    month: string;
    count: number;
    detections: number;
  }>;
  top_regions: Array<{
    region: string;
    count: number;
    risk_level: string;
  }>;
}

export interface AutomatedAnalysisRun {
  zone_ids: number[];
  analysis_type: 'MINING_DETECTION' | 'CHANGE_DETECTION' | 'VEGETATION_ANALYSIS';
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  include_financial_assessment: boolean;
  notification_settings: {
    email_on_completion: boolean;
    email_on_failure: boolean;
    alert_on_high_risk: boolean;
  };
}

class AnalysisService {
  // Récupération des zones prédéfinies
  async getAnalysisZones(): Promise<AnalysisZone[]> {
    try {
      const response = await axiosInstance.get('/analysis/zones/');
      return response.data.results || response.data;
    } catch (error) {
      console.error('Erreur récupération zones analyse:', error);
      throw new Error('Impossible de récupérer les zones d\'analyse');
    }
  }

  // Création d'une zone personnalisée
  async createAnalysisZone(zone: Omit<AnalysisZone, 'id' | 'created_at'>): Promise<AnalysisZone> {
    try {
      const response = await axiosInstance.post('/analysis/zones/', zone);
      return response.data;
    } catch (error) {
      console.error('Erreur création zone analyse:', error);
      throw new Error('Impossible de créer la zone d\'analyse');
    }
  }

  // Récupération des sources satellites disponibles
  async getSatelliteSources(): Promise<SatelliteSource[]> {
    try {
      const response = await axiosInstance.get('/analysis/satellite-sources/');
      return response.data.results || response.data;
    } catch (error) {
      console.error('Erreur récupération sources satellites:', error);
      // Fallback avec sources par défaut
      return [
        {
          id: 'sentinel2',
          name: 'Sentinel-2',
          description: 'Images haute résolution (10m) avec 13 bandes spectrales',
          resolution_meters: 10,
          revisit_days: 5,
          available: true,
          bands: ['B2', 'B3', 'B4', 'B8', 'B11', 'B12'],
        },
        {
          id: 'landsat8',
          name: 'Landsat 8',
          description: 'Images moyennes résolution (30m) avec archive historique',
          resolution_meters: 30,
          revisit_days: 16,
          available: true,
          bands: ['B2', 'B3', 'B4', 'B5', 'B6', 'B7'],
        },
      ];
    }
  }

  // Récupération des indices spectraux disponibles
  async getSpectralIndices(): Promise<SpectralIndex[]> {
    try {
      const response = await axiosInstance.get('/analysis/spectral-indices/');
      return response.data.results || response.data;
    } catch (error) {
      console.error('Erreur récupération indices spectraux:', error);
      // Fallback avec indices par défaut
      return [
        {
          id: 'ndvi',
          name: 'NDVI',
          description: 'Indice de végétation normalisé',
          formula: '(NIR - Red) / (NIR + Red)',
          use_case: 'Détection déforestation',
          bands_required: ['B4', 'B8'],
        },
        {
          id: 'ndwi',
          name: 'NDWI',
          description: 'Indice d\'eau normalisé',
          formula: '(Green - NIR) / (Green + NIR)',
          use_case: 'Détection pollution hydrique',
          bands_required: ['B3', 'B8'],
        },
        {
          id: 'ndmi',
          name: 'NDMI',
          description: 'Indice d\'humidité normalisé',
          formula: '(NIR - SWIR1) / (NIR + SWIR1)',
          use_case: 'Détection perturbation sol',
          bands_required: ['B8', 'B11'],
        },
      ];
    }
  }

  // Lancement d'une nouvelle analyse
  async startAnalysis(parameters: AnalysisParameters, name: string): Promise<AnalysisJob> {
    try {
      const response = await axiosInstance.post('/analysis/jobs/', {
        name,
        parameters,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lancement analyse:', error);
      throw new Error('Impossible de lancer l\'analyse');
    }
  }

  // Récupération des jobs d'analyse
  async getAnalysisJobs(page: number = 1, pageSize: number = 20): Promise<{
    count: number;
    results: AnalysisJob[];
  }> {
    try {
      const response = await axiosInstance.get('/analysis/jobs/', {
        params: { page, page_size: pageSize },
      });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération jobs analyse:', error);
      throw new Error('Impossible de récupérer les analyses');
    }
  }

  // Récupération d'un job spécifique
  async getAnalysisJob(jobId: string): Promise<AnalysisJob> {
    try {
      const response = await axiosInstance.get(`/analysis/jobs/${jobId}/`);
      return response.data;
    } catch (error) {
      console.error('Erreur récupération job analyse:', error);
      throw new Error('Impossible de récupérer l\'analyse');
    }
  }

  // Annulation d'un job
  async cancelAnalysisJob(jobId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axiosInstance.post(`/analysis/jobs/${jobId}/cancel/`);
      return response.data;
    } catch (error) {
      console.error('Erreur annulation job analyse:', error);
      throw new Error('Impossible d\'annuler l\'analyse');
    }
  }

  // Récupération des résultats d'analyse
  async getAnalysisResults(jobId: string): Promise<AnalysisResult> {
    try {
      const response = await axiosInstance.get(`/analysis/jobs/${jobId}/results/`);
      return response.data;
    } catch (error) {
      console.error('Erreur récupération résultats analyse:', error);
      throw new Error('Impossible de récupérer les résultats');
    }
  }

  // Téléchargement du rapport complet
  async downloadAnalysisReport(jobId: string): Promise<Blob> {
    try {
      const response = await axiosInstance.get(`/analysis/jobs/${jobId}/report/`, {
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      console.error('Erreur téléchargement rapport:', error);
      throw new Error('Impossible de télécharger le rapport');
    }
  }

  // Récupération des jobs en cours (pour monitoring temps réel)
  async getRunningJobs(): Promise<AnalysisJob[]> {
    try {
      const response = await axiosInstance.get('/analysis/jobs/running/');
      return response.data.results || response.data;
    } catch (error) {
      console.error('Erreur récupération jobs en cours:', error);
      return [];
    }
  }

  // Validation des paramètres avant lancement
  async validateAnalysisParameters(parameters: AnalysisParameters): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
    estimated_processing_time: number;
    estimated_cost: number;
  }> {
    try {
      const response = await axiosInstance.post('/analysis/validate/', { parameters });
      return response.data;
    } catch (error) {
      console.error('Erreur validation paramètres:', error);
      // Validation côté client basique
      const errors: string[] = [];
      const warnings: string[] = [];

      if (!parameters.start_date || !parameters.end_date) {
        errors.push('Les dates de début et fin sont obligatoires');
      }

      if (new Date(parameters.start_date) >= new Date(parameters.end_date)) {
        errors.push('La date de début doit être antérieure à la date de fin');
      }

      if (parameters.satellite_sources.length === 0) {
        errors.push('Au moins une source satellite doit être sélectionnée');
      }

      if (parameters.cloud_coverage_max > 80) {
        warnings.push('Couverture nuageuse élevée - résultats potentiellement limités');
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings,
        estimated_processing_time: 300, // 5 minutes par défaut
        estimated_cost: 0,
      };
    }
  }

  // ========== NOUVELLES MÉTHODES SPRINT 6 ==========

  // Lancement d'analyse automatisée (API principale Sprint 6)
  async runAutomatedAnalysis(config: AutomatedAnalysisRun): Promise<{
    success: boolean;
    message: string;
    jobs_created: AnalysisJob[];
    estimated_completion: string;
  }> {
    try {
      const response = await axiosInstance.post('/analysis/run/', config);
      return response.data;
    } catch (error) {
      console.error('Erreur lancement analyse automatisée:', error);
      throw new Error('Impossible de lancer l\'analyse automatisée');
    }
  }

  // Évaluation des risques financiers
  async createFinancialRiskAssessment(
    analysisJobId: string,
    customFactors?: {
      gold_price_per_gram?: number;
      environmental_multiplier?: number;
      legal_risk_factor?: number;
    }
  ): Promise<FinancialRiskAssessment> {
    try {
      const response = await axiosInstance.post(`/analysis/jobs/${analysisJobId}/financial-assessment/`, {
        custom_factors: customFactors,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur création évaluation risques financiers:', error);
      throw new Error('Impossible de créer l\'évaluation des risques financiers');
    }
  }

  // Récupération évaluation risques financiers
  async getFinancialRiskAssessment(assessmentId: string): Promise<FinancialRiskAssessment> {
    try {
      const response = await axiosInstance.get(`/analysis/financial-assessments/${assessmentId}/`);
      return response.data;
    } catch (error) {
      console.error('Erreur récupération évaluation risques:', error);
      throw new Error('Impossible de récupérer l\'évaluation des risques');
    }
  }

  // Statistiques globales d'analyse
  async getAnalysisStats(period?: 'week' | 'month' | 'quarter' | 'year'): Promise<AnalysisStats> {
    try {
      const params = period ? { period } : {};
      const response = await axiosInstance.get('/analysis/stats/', { params });
      return response.data;
    } catch (error) {
      console.error('Erreur récupération statistiques analyse:', error);
      throw new Error('Impossible de récupérer les statistiques');
    }
  }

  // Gestion des templates d'analyse
  async getAnalysisTemplates(): Promise<AnalysisTemplate[]> {
    try {
      const response = await axiosInstance.get('/analysis/templates/');
      return response.data.results || response.data;
    } catch (error) {
      console.error('Erreur récupération templates:', error);
      throw new Error('Impossible de récupérer les templates');
    }
  }

  async createAnalysisTemplate(template: Omit<AnalysisTemplate, 'id' | 'created_by' | 'created_by_name' | 'usage_count' | 'created_at'>): Promise<AnalysisTemplate> {
    try {
      const response = await axiosInstance.post('/analysis/templates/', template);
      return response.data;
    } catch (error) {
      console.error('Erreur création template:', error);
      throw new Error('Impossible de créer le template');
    }
  }

  async deleteAnalysisTemplate(templateId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axiosInstance.delete(`/analysis/templates/${templateId}/`);
      return response.data;
    } catch (error) {
      console.error('Erreur suppression template:', error);
      throw new Error('Impossible de supprimer le template');
    }
  }

  // Gestion des analyses programmées
  async getAnalysisSchedules(): Promise<AnalysisSchedule[]> {
    try {
      const response = await axiosInstance.get('/analysis/schedules/');
      return response.data.results || response.data;
    } catch (error) {
      console.error('Erreur récupération programmations:', error);
      throw new Error('Impossible de récupérer les programmations');
    }
  }

  async createAnalysisSchedule(schedule: Omit<AnalysisSchedule, 'id' | 'created_by' | 'created_by_name'>): Promise<AnalysisSchedule> {
    try {
      const response = await axiosInstance.post('/analysis/schedules/', schedule);
      return response.data;
    } catch (error) {
      console.error('Erreur création programmation:', error);
      throw new Error('Impossible de créer la programmation');
    }
  }

  async toggleAnalysisSchedule(scheduleId: string, isActive: boolean): Promise<AnalysisSchedule> {
    try {
      const response = await axiosInstance.patch(`/analysis/schedules/${scheduleId}/`, {
        is_active: isActive,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur modification programmation:', error);
      throw new Error('Impossible de modifier la programmation');
    }
  }

  async deleteAnalysisSchedule(scheduleId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axiosInstance.delete(`/analysis/schedules/${scheduleId}/`);
      return response.data;
    } catch (error) {
      console.error('Erreur suppression programmation:', error);
      throw new Error('Impossible de supprimer la programmation');
    }
  }

  // Analyses en lot pour toutes les zones
  async runBulkAnalysis(
    zoneIds: number[],
    analysisType: 'MINING_DETECTION' | 'CHANGE_DETECTION' | 'VEGETATION_ANALYSIS',
    priority: 'LOW' | 'MEDIUM' | 'HIGH' = 'MEDIUM'
  ): Promise<{
    success: boolean;
    message: string;
    jobs_created: AnalysisJob[];
    total_zones: number;
    estimated_completion: string;
  }> {
    try {
      const response = await axiosInstance.post('/analysis/bulk-run/', {
        zone_ids: zoneIds,
        analysis_type: analysisType,
        priority,
        include_financial_assessment: true,
        notification_settings: {
          email_on_completion: true,
          email_on_failure: true,
          alert_on_high_risk: true,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lancement analyse en lot:', error);
      throw new Error('Impossible de lancer l\'analyse en lot');
    }
  }

  // Rapport consolidé multi-analyses
  async generateConsolidatedReport(
    jobIds: string[],
    includeFinancialAssessment: boolean = true
  ): Promise<{
    report_id: string;
    download_url: string;
    expires_at: string;
  }> {
    try {
      const response = await axiosInstance.post('/analysis/consolidated-report/', {
        job_ids: jobIds,
        include_financial_assessment: includeFinancialAssessment,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur génération rapport consolidé:', error);
      throw new Error('Impossible de générer le rapport consolidé');
    }
  }

  // Comparaison temporelle d'analyses
  async compareAnalyses(
    baseJobId: string,
    compareJobId: string
  ): Promise<{
    comparison_id: string;
    changes_detected: number;
    new_detections: number;
    resolved_detections: number;
    change_percentage: number;
    risk_evolution: 'IMPROVED' | 'STABLE' | 'DETERIORATED';
    detailed_changes: any[];
  }> {
    try {
      const response = await axiosInstance.post('/analysis/compare/', {
        base_job_id: baseJobId,
        compare_job_id: compareJobId,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur comparaison analyses:', error);
      throw new Error('Impossible de comparer les analyses');
    }
  }
}

export default new AnalysisService();