from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from api.serializers.auth_serializers import UserProfileSerializer


class AuthViewSet(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]
    """
    ViewSet pour l'authentification et profil utilisateur
    - GET /api/v1/auth/profile/ - Profil utilisateur connecté
    """

    @action(detail=False, methods=['get'], url_path='profile')
    def get_profile(self, request):
        """
        Récupère le profil de l'utilisateur connecté
        GET /api/v1/auth/profile/
        """
        try:
            serializer = UserProfileSerializer(request.user)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                'error': f'Erreur récupération profil: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['put'], url_path='profile')
    def update_profile(self, request):
        """
        Met à jour le profil de l'utilisateur connecté
        PUT /api/v1/auth/profile/
        """
        try:
            serializer = UserProfileSerializer(request.user, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response({
                    'message': 'Profil mis à jour avec succès',
                    'data': serializer.data
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'error': 'Données invalides',
                    'details': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'error': f'Erreur mise à jour profil: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
